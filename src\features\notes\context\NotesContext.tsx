import React, { createContext, useReducer, Dispatch, ReactNode } from 'react';
import type { Note, NotesState, NotesAction } from '../types';

const initialState: NotesState = {
  notes: {
    'note-1': {
      id: 'note-1',
      title: 'Welcome to PenguinManager Notes',
      content: '• This is your notes workspace\n• Create, organize, and manage your notes\n• Use bullet points and formatting',
      parentId: null,
      children: ['note-2', 'note-3'],
      tags: ['welcome', 'getting-started'],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
    'note-2': {
      id: 'note-2',
      title: 'Getting Started',
      content: '• Click the + button to create new notes\n• Use the search bar to find notes\n• Organize with tags',
      parentId: 'note-1',
      children: ['note-4'],
      tags: ['tutorial'],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
    'note-3': {
      id: 'note-3',
      title: 'Features',
      content: '• Rich text formatting\n• Nested bullet points\n• Tag organization\n• Search functionality',
      parentId: 'note-1',
      children: [],
      tags: ['features', 'documentation'],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
    'note-4': {
      id: 'note-4',
      title: 'First Steps',
      content: '• Create your first note by clicking the + button\n• Add some content\n• Try the formatting tools',
      parentId: 'note-2',
      children: [],
      tags: ['tutorial', 'quick-start'],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
    'note-5': {
      id: 'note-5',
      title: 'Project Ideas',
      content: '• Brainstorm new project concepts\n• Research market opportunities\n• Plan development roadmap',
      parentId: null,
      children: ['note-6', 'note-7'],
      tags: ['projects', 'ideas'],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
    'note-6': {
      id: 'note-6',
      title: 'Web Application',
      content: '• React-based dashboard\n• User authentication\n• Real-time updates\n• Responsive design',
      parentId: 'note-5',
      children: [],
      tags: ['web', 'development'],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
    'note-7': {
      id: 'note-7',
      title: 'Mobile App',
      content: '• Cross-platform development\n• Offline functionality\n• Push notifications\n• App store optimization',
      parentId: 'note-5',
      children: [],
      tags: ['mobile', 'development'],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
  },
  rootIds: ['note-1', 'note-5'],
};

export const NotesContext = createContext<{
  state: NotesState;
  dispatch: Dispatch<NotesAction>;
} | undefined>(undefined);

function notesReducer(state: NotesState, action: NotesAction): NotesState {
  switch (action.type) {
    case 'set': {
      return {
        notes: action.notes,
        rootIds: action.rootIds,
      };
    }
    case 'create': {
      const { note } = action;
      const noteWithTags = { ...note, tags: note.tags || [] };
      const notes = { ...state.notes, [noteWithTags.id]: noteWithTags };
      let rootIds = state.rootIds;
      if (noteWithTags.parentId) {
        // Add to parent's children
        const parent = notes[noteWithTags.parentId];
        if (parent) {
          notes[noteWithTags.parentId] = {
            ...parent,
            children: [...parent.children, noteWithTags.id],
          };
        }
      } else {
        // Add to root
        rootIds = [...state.rootIds, noteWithTags.id];
      }
      return { notes, rootIds };
    }
    case 'update': {
      const { id, updates } = action;
      const note = state.notes[id];
      if (!note) return state;
      return {
        ...state,
        notes: {
          ...state.notes,
          [id]: { ...note, ...updates, updatedAt: new Date().toISOString() },
        },
      };
    }
    case 'delete': {
      const { id } = action;
      const notes = { ...state.notes };
      const note = notes[id];
      if (!note) return state;
      // Remove from parent's children or rootIds
      let rootIds = state.rootIds;
      if (note.parentId) {
        const parent = notes[note.parentId];
        if (parent) {
          notes[note.parentId] = {
            ...parent,
            children: parent.children.filter((childId: string) => childId !== id),
          };
        }
      } else {
        rootIds = rootIds.filter((rootId: string) => rootId !== id);
      }
      // Recursively delete children
      const deleteRecursive = (noteId: string) => {
        const n = notes[noteId];
        if (n) {
          n.children.forEach(deleteRecursive);
          delete notes[noteId];
        }
      };
      deleteRecursive(id);
      return { notes, rootIds };
    }
    case 'moveSubtree': {
      const { id, newParentId } = action;
      const notes = { ...state.notes };
      const note = notes[id];
      if (!note) return state;
      // Remove from old parent's children or rootIds
      let rootIds = state.rootIds;
      if (note.parentId) {
        const oldParent = notes[note.parentId];
        if (oldParent) {
          notes[note.parentId] = {
            ...oldParent,
            children: oldParent.children.filter(childId => childId !== id),
          };
        }
      } else {
        rootIds = rootIds.filter(rootId => rootId !== id);
      }
      // Add to new parent's children or rootIds
      if (newParentId) {
        const newParent = notes[newParentId];
        if (newParent) {
          notes[newParentId] = {
            ...newParent,
            children: [...newParent.children, id],
          };
        }
        notes[id] = { ...note, parentId: newParentId };
      } else {
        rootIds = [...rootIds, id];
        notes[id] = { ...note, parentId: null };
      }
      return { notes, rootIds };
    }
    default:
      return state;
  }
}

export const NotesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(notesReducer, initialState);
  return (
    <NotesContext.Provider value={{ state, dispatch }}>
      {children}
    </NotesContext.Provider>
  );
}; 
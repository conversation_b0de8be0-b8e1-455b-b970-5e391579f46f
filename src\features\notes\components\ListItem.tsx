import React from 'react';
import type { Note } from '../types';

interface ListItemProps {
  note: Note;
  onClick: () => void;
  onDelete: (e: React.MouseEvent) => void;
  onDuplicate: (e: React.MouseEvent) => void;
  onTag: (e: React.MouseEvent) => void;
  isSearchResult?: boolean;
}

const ListItem: React.FC<ListItemProps> = ({
  note,
  onClick,
  onDelete,
  onDuplicate,
  onTag,
  isSearchResult = false
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays === 2) {
      return 'Yesterday';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getPreviewText = (content: string) => {
    if (!content) return '(Empty note)';
    return content.length > 100 ? content.substring(0, 100) + '...' : content;
  };

  return (
    <div 
      className={`relative bg-[#23272f] rounded-xl border border-[#23272f] hover:border-blue-600 shadow-sm transition p-5 min-h-[120px] cursor-pointer group ${
        isSearchResult ? 'ring-2 ring-blue-500/50' : ''
      }`}
      onClick={onClick}
    >
      <div className="font-semibold text-lg text-gray-100 truncate mb-2">
        {note.title || '(Untitled Note)'}
      </div>

      {note.content && (
        <div className="text-sm text-gray-400 mb-3 line-clamp-2">
          {getPreviewText(note.content)}
        </div>
      )}

      {/* Tags */}
      {note.tags && note.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {note.tags.slice(0, 3).map(tag => (
            <span
              key={tag}
              className="inline-block px-2 py-0.5 bg-blue-600/20 text-blue-400 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
          {note.tags.length > 3 && (
            <span className="inline-block px-2 py-0.5 bg-gray-600/20 text-gray-400 text-xs rounded-full">
              +{note.tags.length - 3}
            </span>
          )}
        </div>
      )}

      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex flex-col gap-1">
          <span>Created {formatDate(note.createdAt)}</span>
          <span>Updated {formatDate(note.updatedAt)}</span>
        </div>
        {note.children.length > 0 && (
          <span className="flex items-center gap-1">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
            {note.children.length} sub-items
          </span>
        )}
      </div>
      
      <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition">
        <button 
          onClick={onDelete} 
          className="text-gray-400 hover:text-red-400 p-1 rounded hover:bg-red-400/10" 
          title="Delete"
        >
          <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <button 
          onClick={onDuplicate} 
          className="text-gray-400 hover:text-blue-400 p-1 rounded hover:bg-blue-400/10" 
          title="Duplicate"
        >
          <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <rect x="9" y="9" width="13" height="13" rx="2" />
            <rect x="3" y="3" width="13" height="13" rx="2" />
          </svg>
        </button>
        <button 
          onClick={onTag} 
          className="text-gray-400 hover:text-green-400 p-1 rounded hover:bg-green-400/10" 
          title="Tag"
        >
          <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M7 7h.01M3 11.5V7a2 2 0 0 1 2-2h4.5a2 2 0 0 1 1.414.586l7.5 7.5a2 2 0 0 1 0 2.828l-4.5 4.5a2 2 0 0 1-2.828 0l-7.5-7.5A2 2 0 0 1 3 11.5z" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default ListItem; 
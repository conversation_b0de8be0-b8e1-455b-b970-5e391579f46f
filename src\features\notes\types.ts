export interface Note {
  id: string;
  title: string;
  content: string;
  parentId: string | null;
  children: string[];
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface NotesState {
  notes: Record<string, Note>;
  rootIds: string[];
}

export type NotesAction =
  | { type: 'set'; notes: Record<string, Note>; rootIds: string[] }
  | { type: 'create'; note: Note }
  | { type: 'update'; id: string; updates: Partial<Note> }
  | { type: 'delete'; id: string }
  | { type: 'moveSubtree'; id: string; newParentId: string | null }; 
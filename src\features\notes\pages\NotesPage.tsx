import React, { useState, useContext, useMemo } from 'react';
import { NotesContext } from '../context/NotesContext';
import { useNavigate } from 'react-router-dom';
import NotesFilter from '../components/NotesFilter';
import ListItem from '../components/ListItem';
import { searchNotes } from '../utils/treeHelpers';
import { v4 as uuidv4 } from 'uuid';
import NewNoteModal from '../components/NewNoteModal';

const NotesPageContent: React.FC = () => {
  const { state, dispatch } = useContext(NotesContext)!;
  const [filteredNoteIds, setFilteredNoteIds] = useState<string[]>([]);
  const [showNewNoteModal, setShowNewNoteModal] = useState(false);
  const navigate = useNavigate();

  // Get all available tags from all notes
  const availableTags = useMemo(() => {
    const allTags = new Set<string>();
    Object.values(state.notes).forEach(note => {
      note.tags.forEach(tag => allTags.add(tag));
    });
    return Array.from(allTags).sort();
  }, [state.notes]);

  const handleAddNote = () => {
    setShowNewNoteModal(true);
  };

  const handleCreateNote = (title: string, content: string, tags: string[]) => {
    const id = uuidv4();
    const now = new Date().toISOString();
    dispatch({
      type: 'create',
      note: {
        id,
        title,
        content,
        parentId: null,
        children: [],
        tags,
        createdAt: now,
        updatedAt: now,
      },
    });
    setShowNewNoteModal(false);
  };

  const handleDeleteNote = (id: string) => {
    dispatch({ type: 'delete', id });
  };

  const handleDuplicateNote = (id: string) => {
    const note = state.notes[id];
    if (!note) return;
    const newId = uuidv4();
    const now = new Date().toISOString();
    dispatch({
      type: 'create',
      note: {
        ...note,
        id: newId,
        createdAt: now,
        updatedAt: now,
        title: note.title + ' (Copy)',
        content: note.content,
        parentId: null,
        children: [],
        tags: [...note.tags],
      },
    });
  };

  // Placeholder for tag action
  const handleTagNote = (id: string) => {
    // Implement tag logic here
  };

  return (
    <div className="p-4 pl-8 flex flex-col w-full min-h-screen border-l border-gray-800 bg-[#181b22]">
      <div className="flex items-center mb-4 w-full max-w-4xl">
        <button onClick={handleAddNote} className="mr-4 px-3 py-1 rounded border border-gray-600 text-gray-100 hover:bg-gray-800 transition">
          + New Note
        </button>
      </div>

      <div className="max-w-6xl w-full mx-auto">
        <NotesFilter
          notes={state.notes}
          onFilteredNotesChange={setFilteredNoteIds}
          availableTags={availableTags}
        />

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredNoteIds.map((id) => {
            const note = state.notes[id];
            if (!note) return null;

            return (
              <ListItem
                key={id}
                note={note}
                onClick={() => navigate(`/notes/${id}`)}
                onDelete={(e) => { e.stopPropagation(); handleDeleteNote(id); }}
                onDuplicate={(e) => { e.stopPropagation(); handleDuplicateNote(id); }}
                onTag={(e) => { e.stopPropagation(); handleTagNote(id); }}
                isSearchResult={false}
              />
            );
          })}
        </div>
      </div>

      {/* New Note Modal */}
      {showNewNoteModal && (
        <NewNoteModal
          isOpen={showNewNoteModal}
          onClose={() => setShowNewNoteModal(false)}
          onCreateNote={handleCreateNote}
          availableTags={availableTags}
        />
      )}
    </div>
  );
};

const NotesPage: React.FC = () => <NotesPageContent />;
export default NotesPage; 
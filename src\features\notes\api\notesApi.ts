import type { Note } from '../types';

// TODO: Replace with real API client (Apollo or Axios)
export async function fetchNotes(): Promise<Note[]> {
  return [];
}

export async function createNote(note: Partial<Note>): Promise<Note> {
  return { ...note, id: 'temp', title: '', content: '', parentId: null, children: [], createdAt: '', updatedAt: '' } as Note;
}

export async function updateNote(id: string, updates: Partial<Note>): Promise<Note> {
  return { id, ...updates } as Note;
}

export async function deleteNote(id: string): Promise<void> {
  return;
}

export async function moveSubtree(id: string, newParentId: string | null): Promise<void> {
  return;
} 